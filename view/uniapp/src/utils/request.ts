import pinia from "@/store/index";
import { useUserStore } from "@/store/user";
import { useConfigStore } from "@/store/config";
import { useI18nStore } from "@/store/i18n";
import { formatArguments, isOverseas, currFullPath } from "@/utils";
import { KEY, aesEncrypt } from "@/utils/crypto";
export const getSecret = () => {
    const currentTimeStamp = Math.floor(Date.now() / 1000);
    return aesEncrypt(currentTimeStamp, KEY);
};

const userStore = useUserStore(pinia);

// 定义请求配置接口，添加新的参数
interface RequestConfig {
    url: string;
    method?: "GET" | "POST" | "post" | "get";
    data?: any;
    params?: any;
    header?: any;
    noSkipLogin?: boolean;
    prefix?: string;
    cancelPrevious?: boolean;
    requestKey?: string;
}

// 定义响应数据结构
interface ResponseData {
    code?: number;
    message: string;
    data: any;
}

// #ifdef H5
export const baseUrl = import.meta.env.VITE_API_URL || location.origin;
// #endif

// #ifndef H5
export const baseUrl = import.meta.env.VITE_API_URL;
// #endif

// 存储活跃的请求，键为请求标识，值为请求任务
const activeRequests: Map<string, UniApp.RequestTask> = new Map();

export const handleLogin = () => {
    const configStore = useConfigStore();

    // 检查当前页面路径
    const fullPath = currFullPath();
    console.log("当前页面路径:", fullPath);
    console.log("客户端类型:", configStore.XClientType);

    // 检查是否是会员中心页面
    const isUserPage = fullPath.includes("/pages/user/index") || fullPath.includes("pages/user/index");

    // 优先在会员中心页面显示弹窗，其次在微信环境下显示弹窗
    if (isUserPage) {
        console.log("会员中心页面 - 显示登录弹窗");
        userStore.clear();
        userStore.setAuthType("wechatLogin");
    } else if (configStore.XClientType === "miniProgram" || configStore.XClientType === "wechat") {
        console.log("微信环境 - 显示登录弹窗");
        userStore.clear();
        userStore.setAuthType("wechatLogin");
    } else {
        console.log("其他环境 - 跳转到登录页面");
        userStore.clear();
        if (fullPath.indexOf("login") > -1) return;
        const url = `/pages/login/index${fullPath ? `?url=${fullPath}` : ""}`;
        setTimeout(() => {
            uni.navigateTo({ url });
        }, 200);
    }
};

export default function request<T extends ResponseData>(config: RequestConfig): Promise<T["data"]> {
    const i18nStore = useI18nStore();
    const configStore = useConfigStore(pinia);
    config.noSkipLogin = config.noSkipLogin || false;
    const method = (config.method || "GET").toUpperCase();
    const prefix = config.prefix || import.meta.env.VITE_API_PREFIX;
    const url = `${baseUrl}${prefix}${config.url}`;
    const header = {
        Authorization: "Bearer " + (uni.getStorageSync("token") ? uni.getStorageSync("token") : null),
        ...config.header,
        "X-Client-Type": configStore.XClientType,
        "X-Locale-Code": isOverseas() ? i18nStore.defaultLocaleCode || uni.getLocale() : "",
        Secret: getSecret()
    };
    const data = method === "GET" ? formatArguments(config.params) : config.data;

    if (config.cancelPrevious && config.requestKey) {
        const previousRequest = activeRequests.get(config.requestKey);
        if (previousRequest) {
            previousRequest.abort();
            activeRequests.delete(config.requestKey);
        }
    }

    return new Promise<T["data"]>((resolve, reject) => {
        const requestTask = uni.request({
            url,
            method: method as "GET" | "POST",
            data,
            header,
            success: (response) => {
                if (config.requestKey) {
                    activeRequests.delete(config.requestKey);
                }
                const data = response.data as T;
                if (data.code !== 0) {
                    if (data.code === 401 && !config.noSkipLogin) {
                        handleLogin();
                        reject({ message: "请登录" });
                    }
                    reject(data);
                } else {
                    resolve(data.data);
                }
            },
            fail: (err) => {
                if (config.requestKey) {
                    activeRequests.delete(config.requestKey);
                }
                if (err && err.errMsg && err.errMsg.indexOf("abort") !== -1) {
                    reject({ code: -1, message: "请求已取消", data: null });
                } else {
                    reject(err);
                }
            }
        });

        if (config.requestKey) {
            activeRequests.set(config.requestKey, requestTask);
        }
    });
}

export function abortRequest(requestKey: string): boolean {
    const request = activeRequests.get(requestKey);
    if (request) {
        request.abort();
        activeRequests.delete(requestKey);
        return true;
    }
    return false;
}

export function abortAllRequests(): void {
    activeRequests.forEach((request) => request.abort());
    activeRequests.clear();
}
