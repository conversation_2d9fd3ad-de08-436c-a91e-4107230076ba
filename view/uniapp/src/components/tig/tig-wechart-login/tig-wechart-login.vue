<template>
    <tig-popup v-model:show="show" position="bottom" :z-index="1000" :round="20" @close="close">
        <template v-if="show">
            <view class="wechart-login-content">
                <!-- 顶部拖拽指示器 -->
                <view class="drag-indicator"></view>

                <!-- 标题区域 -->
                <view class="login-header">
                <!-- <view class="logo-text">SANKUWA</view> -->

                    <image class="login-logo" src="/static/images/logo/17539266366wy0wMCDGBztfGwSKr.jpeg" />
                    <view class="login-title">探索自然｜定义生活</view>
                </view>



                  <!-- 协议区域 -->
                  <view class="rule-text">
                        <tig-checkbox v-model:checked="isChecked" color="#07C160" />
                        <view class="rule-xieyi">
                            <text @click="isChecked = !isChecked">{{ $t("登录即为同意") }}</text>
                            <text class="special-color" @click="showAgreement">{{ $t("《商城用户服务协议》") }}</text>
                        </view>
                    </view>


                <view class="wechart-login-box">
                    <!-- #ifdef MP-WEIXIN -->
                    <template v-if="isChecked">
                        <tig-button
                            :custom-style="{
                                'border-radius': '16rpx',
                                'height': '100rpx',
                                'background': 'black',
                                'color': 'white',
                                'font-size': '32rpx',
                                'font-weight': '500',
                                'border-color': 'black',
                                'margin-top': '15rpx'
                            }"
                            open-type="getPhoneNumber"
                            shape="square"
                            :loading="loginLoading"
                            @get-phone-number="getPhoneNumber"
                        >
                            <view class="button-content">
                                <!-- <image class="button-icon" src="/static/images/common/<EMAIL>" /> -->
                                <text>{{ $t("手机号快捷登录") }}</text>
                            </view>
                        </tig-button>
                    </template>
                    <template v-else>
                        <tig-button
                            :custom-style="{
                                'border-radius': '16rpx',
                                'height': '100rpx',
                                'background': 'black',
                                'color': 'white',
                                'font-size': '32rpx',
                                'border-color': 'black',
                                'margin-top': '15rpx'
                            }"
                            shape="square"
                            @click="mobileLogin"
                        >
                            <view class="button-content">
                                <!-- <image class="button-icon" src="/static/images/common/<EMAIL>" /> -->
                                <text>手机号快捷登录</text>
                            </view>
                        </tig-button>
                    </template>
                    <!-- #endif -->

                    <!-- #ifndef MP-WEIXIN -->
                    <template v-if="isChecked">
                        <tig-button
                            :custom-style="{
                                'border-radius': '16rpx',
                                'height': '100rpx',
                                'background': '#07C160',
                                'color': '#fff',
                                'font-size': '32rpx',
                                'font-weight': '500',
                                'box-shadow': '0 4rpx 12rpx rgba(7, 193, 96, 0.3)'
                            }"
                            shape="square"
                            :loading="loginLoading"
                            @click="wechatLogin"
                        >
                            <view class="button-content">
                                <image class="button-icon" src="/static/images/common/wechat_pay.png" />
                                <text>{{ $t("微信授权登录") }}</text>
                            </view>
                        </tig-button>
                    </template>
                    <template v-else>
                        <tig-button
                            :custom-style="{
                                'border-radius': '16rpx',
                                'height': '100rpx',
                                'background': '#E5E5E5',
                                'color': '#999',
                                'font-size': '32rpx'
                            }"
                            shape="square"
                            @click="mobileLogin"
                        >
                            <view class="button-content">
                                <image class="button-icon" src="/static/images/common/wechat_pay.png" />
                                <text>{{ $t("微信授权登录") }}</text>
                            </view>
                        </tig-button>
                    </template>
                    <!-- #endif -->

                  

                    <!-- 其他登录方式 -->
                    <!-- <view class="other-login">
                        <view class="divider-line"></view>
                        <text class="divider-text">其他登录方式</text>
                        <view class="divider-line"></view>
                    </view>

                    <view class="login-methods">
                        <view class="method-item" @click="handleLink('mobile')">
                            <image class="method-icon" src="/static/images/common/<EMAIL>" />
                            <text class="method-text">{{ $t("手机登录") }}</text>
                        </view>
                        <view class="method-item" @click="handleLink('password')">
                            <image class="method-icon" src="/static/images/common/<EMAIL>" />
                            <text class="method-text">{{ $t("账号登录") }}</text>
                        </view>
                    </view> -->


                </view>
            </view>
        </template>
    </tig-popup>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { getOauthInfo, getOauthUrl, getWechartMobile, setMiniProgramOpenid } from "@/api/login/login";
import { useUserStore } from "@/store/user";
import { useConfigStore } from "@/store/config";
import { onShow } from "@dcloudio/uni-app";
import { useI18n } from "vue-i18n";

// 声明微信小程序全局对象
declare const wx: any;

const { t } = useI18n();

const emit = defineEmits(["loginSuccess"]);

const userStore = useUserStore();
const configStore = useConfigStore();

const show = computed({
    get() {
        return !!userStore.authType;
    },
    set(_val) {
        userStore.setAuthType("");
    }
});

const isChecked = ref(false);
const loginLoading = ref(false);
const showAgreement = () => {
    uni.navigateTo({
        url: "/pages/login/mallAgreement"
    });
};
const mobileLogin = () => {
    if (!isChecked.value) {
        return uni.showToast({
            title: t("请先同意用户协议"),
            duration: 1500,
            icon: "none"
        });
    }

    // 提示用户需要先同意协议才能使用快捷登录
    uni.showToast({
        title: t("请先勾选同意协议"),
        duration: 1500,
        icon: "none"
    });
};

//微信授权登录
const wechatLogin = async () => {
    const result = await getOauthUrl({ url: location.origin + location.pathname });
    window.location.href = result.url;
};

//获取用户信息
const getWechatInfo = async (code: string) => {
    const result = await getOauthInfo({ code: code });
    if (result.type == 1) {
        //已注册过，直接登录
        userStore.setToken(result.token);
        await userStore.getUserInfo();
        emit("loginSuccess");
        userStore.setAuthType("");
    }
    if (result.type == 2 && configStore.wechatRegisterBindPhone === 1) {
        //跳转绑定手机页面
        uni.setStorageSync("openData", result.openData);
        uni.navigateTo({
            url: "/pages/user/bindMobilePhone/index"
        });
    }
};

const handleLink = (type: string) => {
    userStore.setAuthType("");
    uni.navigateTo({
        url: `/pages/login/index?loginType=${type}`
    });
};

const getPhoneNumber = async (e: any) => {
    try {
        const result = await getWechartMobile({ code: e.detail.code });
        userStore.setToken(result);
        userStore.getUserInfo();
        emit("loginSuccess");
        userStore.setAuthType("");
        // #ifdef MP-WEIXIN
        updateUserOpenId();
        // #endif
    } catch (error: any) {
        console.error(error);
        uni.showToast({
            title: error.message,
            icon: "none"
        });
        userStore.setAuthType("");
    }
};
const updateUserOpenId = async () => {
    wx.login({
        success: async (res: any) => {
            try {
                await setMiniProgramOpenid({
                    code: res.code
                });
            } catch (error) {
                console.error("更新用户OpenId失败:", error);
            }
        },
        fail: (err: any) => {
            console.log("微信登录失败:", err);
        }
    });
};

const close = () => {
    userStore.setAuthType("");
};

const getUlParams = (url: string) => {
    const params: AnyObject = {};
    const urlParams = url.split("?")[1];
    if (urlParams) {
        urlParams.split("&").forEach((item: string) => {
            const [key, value] = item.split("=");
            params[key] = value;
        });
    }
    return params;
};

onShow(() => {
    if (configStore.XClientType === "wechat" && !uni.getStorageSync("token")) {
        const params = getUlParams(location.href);
        if (params.code && params.code.length > 12) {
            //授权
            getWechatInfo(params.code);
        }
    }
});
</script>

<style lang="scss" scoped>
.wechart-login-content {
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 0 40rpx 60rpx;
    min-height: 30vh;
    position: relative;
}

/* 顶部拖拽指示器 */
.drag-indicator {
    width: 80rpx;
    height: 8rpx;
    background: #E5E5E5;
    border-radius: 4rpx;
    margin: 20rpx auto 0;
}

/* 标题区域 */
.login-logo {
    padding: 40rpx 0 10rpx;
    width: 50%;
    height: 50px;
    margin-left: 95px;
}

    // .logo-text {
    //         font-size: 48rpx;
    //         font-weight: 600;
    //         color: #333;
    //         letter-spacing: 2rpx;
    //         position: relative;
    //         padding: 0 20rpx;
    //         position: relative;
    //         top: -27rpx;
            
    //         &::after {
    //             content: '';
    //             position: absolute;
    //             bottom: -8rpx;
    //             left: 50%;
    //             transform: translateX(-50%);
    //             width: 80%;
    //             height: 4rpx;
    //             background: linear-gradient(to right, transparent, #333, transparent);
    //             border-radius: 2rpx;
    //         }
    //     }

.login-title {
    font-size: 35rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    text-align: center;

}

.login-subtitle {
    font-size: 28rpx;
    color: #666;
}

.wechart-login-box {
    .button-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;
        font-weight: 500;
    }

    .button-icon {
        width: 40rpx;
        height: 40rpx;
    }
}

/* 协议区域 */
.rule-text {
    font-size: 26rpx;
    color: #999;
    margin-top: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx 20rpx;
}

.rule-xieyi {
    display: flex;
    align-items: center;
    margin-left: 12rpx;
    flex-wrap: wrap;
}

.special-color {
    color: black;
    margin-left: 8rpx;
}

/* 其他登录方式分割线 */
.other-login {
    display: flex;
    align-items: center;
    margin: 60rpx 0 40rpx;

    .divider-line {
        flex: 1;
        height: 1rpx;
        background: #E5E5E5;
    }

    .divider-text {
        font-size: 24rpx;
        color: #999;
        padding: 0 24rpx;
        white-space: nowrap;
    }
}

/* 登录方式图标 */
.login-methods {
    display: flex;
    justify-content: center;
    gap: 80rpx;
    margin-top: 20rpx;
}

.method-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &:active {
        background: #F5F5F5;
        transform: scale(0.95);
    }
}

.method-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 12rpx;
}

.method-text {
    font-size: 24rpx;
    color: #666;
    text-align: center;
}

/* 按钮样式重置 */
.btn2-css3 {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    padding: 0;
    font-size: 32rpx;
    font-weight: 500;
}
</style>
