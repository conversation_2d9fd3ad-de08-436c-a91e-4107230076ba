<template>
    <tig-layout>
        <!-- 自定义导航栏 -->
        <view class="custom-navbar">
            <view class="status-bar"></view>
            <view class="navbar-content">
                <view class="search-btn" @click="toSearch">
                    <image 
                        src="/static/images/common/search.svg" 
                        class="search-icon"
                        mode="aspectFit"
                    />
                </view>
                <view class="navbar-title">商户名称</view>
                <view class="navbar-right"></view>
            </view>
        </view>

        <!-- 页面内容 -->
        <view class="page-container" :style="containerStyle">
            <!-- 顶部二级分类标签 -->
            <view class="top-categories">
                <scroll-view 
                    scroll-x="true" 
                    class="category-scroll"
                    show-scrollbar="false"
                    enhanced="true"
                    :scroll-with-animation="true"
                >
                    <view class="category-tabs">
                        <view 
                            v-for="(category, index) in secondLevelCategories" 
                            :key="category.categoryId"
                            :class="['category-tab', { active: currentSecondLevelId === category.categoryId }]"
                            @click="selectSecondLevel(category)"
                        >
                            <text class="category-text">{{ category.categoryName }}</text>
                            <view v-if="currentSecondLevelId === category.categoryId" class="active-line"></view>
                        </view>
                    </view>
                </scroll-view>
            </view>

            <!-- 主体内容 -->
            <view class="main-content">
                <!-- 左侧三级分类 -->
                <view class="left-sidebar">
                    <scroll-view scroll-y="true" class="sidebar-scroll">
                        <view 
                            v-for="(category, index) in thirdLevelCategories" 
                            :key="category.categoryId"
                            :class="['sidebar-item', { active: currentThirdLevelId === category.categoryId }]"
                            @click="selectThirdLevel(category)"
                        >
                            <text class="sidebar-text">{{ category.categoryName }}</text>
                        </view>
                        <!-- 返回按钮 -->
                        <view class="back-item" @click="goBackToIndex">
                            <text class="back-text">返回分类</text>
                            <image 
                                src="/static/images/common/<EMAIL>" 
                                class="back-icon"
                                mode="aspectFit"
                            />
                        </view>
                    </scroll-view>
                </view>

                <!-- 右侧商品网格 -->
                <view class="right-content">
                    <scroll-view 
                        scroll-y="true" 
                        class="product-scroll"
                        @scrolltolower="loadMoreProducts"
                        lower-threshold="100"
                    >
                        <view v-if="loading && currentPage === 1" class="loading-wrapper">
                            <view class="loading-spinner"></view>
                            <text class="loading-text">加载中...</text>
                        </view>

                        <view v-else class="product-grid">
                            <view 
                                v-for="(product, index) in productList" 
                                :key="product.productId"
                                class="product-item"
                                @click="navigateToProduct(product)"
                            >
                                <view class="product-image-wrapper">
                                    <image 
                                        :src="product.picThumb" 
                                        class="product-image"
                                        mode="aspectFill"
                                        :lazy-load="true"
                                    />
                                </view>
                                <view class="product-info">
                                    <text class="product-name">{{ product.productName }}</text>
                                    <text class="product-price">¥ {{ product.productPrice }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 加载更多状态 -->
                        <view v-if="isLoadingMore" class="load-more">
                            <view class="load-more-spinner"></view>
                            <text class="load-more-text">加载更多...</text>
                        </view>

                        <!-- 没有更多数据 -->
                        <view v-if="!loading && !isLoadingMore && productList.length > 0 && currentPage >= totalPages" class="no-more">
                            <text class="no-more-text">没有更多商品了</text>
                        </view>

                        <!-- 空状态 -->
                        <view v-if="!loading && productList.length === 0" class="empty-state">
                            <text class="empty-text">暂无商品</text>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </view>

        <!-- 底部导航栏 -->
        <tig-tabbar></tig-tabbar>
    </tig-layout>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import { onLoad, onShow, onHide } from "@dcloudio/uni-app";
import { getCategoryAll } from "@/api/productCate/productCate";
import { getCategoryProduct } from "@/api/search/search";
import { useConfigStore } from "@/store/config";
import { useTabbarStore } from "@/store/tabbar";

const configStore = useConfigStore();
const tabbarStore = useTabbarStore();

interface Category {
    categoryId: number;
    categoryName: string;
    categoryPic?: string;
    children?: Category[];
}

interface Product {
    productId: number;
    productName: string;
    productPrice: number;
    picThumb: string;
}

// 响应式数据
const currentCategoryId = ref(0);
const currentCategoryName = ref('');
const currentSecondLevelId = ref(0);
const currentThirdLevelId = ref(0);
const secondLevelCategories = ref<Category[]>([]);
const thirdLevelCategories = ref<Category[]>([]);
const productList = ref<Product[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const isLoadingMore = ref(false);

// 计算容器样式
const containerStyle = computed(() => {
    const navbarHeight = 100;
    const statusBarHeight = configStore.safeAreaInsets.top || 44;
    const tabbarHeight = tabbarStore.currentActiveValue > -1 ? 50 : 0;
    const totalNavHeight = navbarHeight + statusBarHeight;
    
    return {
        paddingTop: `${totalNavHeight}rpx`,
        paddingBottom: `${tabbarHeight}rpx`,
        minHeight: `calc(100vh - ${totalNavHeight}rpx - ${tabbarHeight}rpx)`
    };
});

// 页面加载时获取参数
onLoad((options) => {
    if (options && options.categoryId) {
        currentCategoryId.value = parseInt(options.categoryId);
        currentCategoryName.value = decodeURIComponent(options.categoryName || '');
        loadCategoryData();
    }
});

// 加载分类数据
const loadCategoryData = async () => {
    try {
        const result = await getCategoryAll();
        const allCategories = result || [];
        
        // 找到当前一级分类
        const currentCategory = allCategories.find((cat: Category) => cat.categoryId === currentCategoryId.value);
        
        if (currentCategory && currentCategory.children) {
            secondLevelCategories.value = currentCategory.children;
            
            // 默认选择第一个二级分类
            if (secondLevelCategories.value.length > 0) {
                selectSecondLevel(secondLevelCategories.value[0]);
            }
        }
    } catch (error) {
        console.error('加载分类数据失败:', error);
        uni.showToast({
            title: '加载失败，请重试',
            icon: 'none'
        });
    }
};

// 选择二级分类
const selectSecondLevel = (category: Category) => {
    currentSecondLevelId.value = category.categoryId;
    
    if (category.children && category.children.length > 0) {
        thirdLevelCategories.value = [
            // { categoryId: category.categoryId, categoryName: '推荐单品' },
            ...category.children
        ];
        // 默认选择第一个三级分类
        selectThirdLevel(thirdLevelCategories.value[0]);
    } else {
        thirdLevelCategories.value = [
            // { categoryId: category.categoryId, categoryName: '推荐单品' }
        ];
        // 如果没有三级分类，直接使用二级分类加载商品
        currentThirdLevelId.value = category.categoryId;
        currentPage.value = 1;
        productList.value = [];
        loadProductList();
    }
};

// 选择三级分类
const selectThirdLevel = (category: Category) => {
    currentThirdLevelId.value = category.categoryId;
    currentPage.value = 1;
    productList.value = [];
    loadProductList();
};

// 加载商品列表
const loadProductList = async (isLoadMore = false) => {
    try {
        if (isLoadMore) {
            isLoadingMore.value = true;
        } else {
            loading.value = true;
        }
        
        // 构造查询参数
        const params = {
            cat: currentThirdLevelId.value,
            page: currentPage.value,
            size: 20
        };
        
        const result = await getCategoryProduct(params);
        const newProducts = result?.records || [];
        
        if (isLoadMore) {
            productList.value = [...productList.value, ...newProducts];
        } else {
            productList.value = newProducts;
        }
        
        // 更新分页信息
        totalPages.value = result?.pages || 1;
        
    } catch (error) {
        console.error('加载商品列表失败:', error);
        uni.showToast({
            title: '加载商品失败',
            icon: 'none'
        });
    } finally {
        loading.value = false;
        isLoadingMore.value = false;
    }
};

// 加载更多商品
const loadMoreProducts = () => {
    if (currentPage.value < totalPages.value && !isLoadingMore.value) {
        currentPage.value++;
        loadProductList(true);
    }
};

// 返回上一页
const goBack = () => {
    uni.navigateBack();
};

// 跳转到搜索页面
const toSearch = () => {
    uni.navigateTo({
        url: "/pages/search/index"
    });
};

// 跳转到商品详情
const navigateToProduct = (product: Product) => {
    uni.navigateTo({
        url: `/pages/product/index?id=${product.productId}`
    });
};

// 返回一级分类页面
const goBackToIndex = () => {
    uni.navigateTo({
        url: "/pages/productCate/index"
    });
};

// 页面显示时设置底部导航栏
onShow(() => {
    tabbarStore.currRoute = 'category';
});

// 页面隐藏时
onHide(() => {
    // 页面隐藏逻辑
});

onMounted(() => {
    // 页面挂载后的初始化逻辑
});
</script>

<style lang="scss" scoped>
/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
}

.status-bar {
    height: var(--status-bar-height);
    background: #fff;
}

.navbar-content {
    height: 85rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    margin-top: 35rpx;
}

.search-btn {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.search-icon {
    width: 105rpx;
    height: 45rpx;
    opacity: 0.8;
    position: relative;
    left: 10px;
}

.navbar-right {
    width: 50rpx;
}

.navbar-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
    text-align: center;
}

/* 页面容器 */
.page-container {
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    top: 10rpx;
}

/* 顶部二级分类标签 */
.top-categories {
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    height: 55px;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    z-index: 3;

}

.category-scroll {
    white-space: nowrap;
}

.category-tabs {
    display: flex;
    padding: 0 20rpx;
}

.category-tab {
    position: relative;
    padding: 20rpx 32rpx;
    font-size: 28rpx;
    color: #666;
    white-space: nowrap;
    font-weight: 400;
    
    &.active {
        color: #333;
        font-weight: 500;
        
        .active-line {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 6rpx;
            background: #333;
            border-radius: 3rpx;
        }
    }
}

/* 主体内容 */
.main-content {
    flex: 1;
    display: flex;
    background: #f5f5f5;
}

/* 左侧边栏 */
.left-sidebar {
    width: 200rpx;
    background: #F5F5F5;
    border-right: 1rpx solid #f0f0f0;
    margin-top: 20px;
}

.sidebar-scroll {
    height: 100%;
}

.sidebar-item {
    padding: 32rpx 20rpx;
    border-bottom: 1rpx solid #f8f8f8;
    
    &.active {
        background: #f5f5f5;
        
        .sidebar-text {
            color: #333;
            font-weight: 600;
        }
    }
}

.sidebar-text {
    font-size: 26rpx;
    color: #666;
    text-align: left;
    line-height: 1.4;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    left: 13px;
}

/* 返回按钮样式 */
.back-item {
    padding: 32rpx 20rpx;
    border-bottom: 1rpx solid #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f0f0f0;
}

.back-text {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
    position: relative;
    left: 13px;
}

.back-icon {
    width: 32rpx;
    height: 32rpx;
    opacity: 0.8;
}

/* 右侧内容区 */
.right-content {
    flex: 1;
    background: #f5f5f5;
    margin-top: 10px;
    margin-right: 25px;
}

.product-scroll {
    height: 100%;
    padding: 20rpx;
}

/* 加载状态 */
.loading-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 200rpx 0;
}

.loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 商品网格 */
.product-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
}



.product-image-wrapper {
    position: relative;
    width: 100%;
    height: 280rpx;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.product-info {
    padding: 15rpx;
    text-align: left;
    position: relative;
    right: 8px;
}

.product-name {
    font-size: 22rpx;
    color: #333;
    line-height: 1.4;
    margin-bottom: 8rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-align: left;
}

.product-price {
    font-size: 24rpx;
    color: #333;
    font-weight: 600;
    text-align: left;
}

/* 加载更多状态 */
.load-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40rpx 0;
}

.load-more-spinner {
    width: 40rpx;
    height: 40rpx;
    border: 3rpx solid #f3f3f3;
    border-top: 3rpx solid #666;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 20rpx;
}

.load-more-text {
    font-size: 26rpx;
    color: #666;
}

/* 没有更多数据 */
.no-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40rpx 0;
}

.no-more-text {
    font-size: 26rpx;
    color: #999;
    text-align: center;
    margin-top: 20px;
}

/* 空状态 */
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 200rpx 0;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
}

/* 兼容小程序 */
page {
    background-color: #f5f5f5 !important;
}
</style> 