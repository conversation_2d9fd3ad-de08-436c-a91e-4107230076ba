<!-- 编辑收货地址 -->
<template>
    <!-- 自定义标题栏 -->
    <view class="custom-header">
        <view class="back-btn" @click="goBack">
            <image src="@/static/images/common/<EMAIL>" class="back-icon" />
        </view>
        <view class="header-title">{{ title }}</view>
        <view class="header-placeholder"></view>
    </view>
    
    <view class="address-edit-main">
        <view class="address-edit-content">
            <uni-forms ref="formRef" :model-value="form" label-width="140rpx">
                <uni-forms-item :label="$t('收货人')" name="consignee" required>
                    <uni-easyinput 
                        v-model="form.consignee" 
                        primary-color="rgb(192, 196, 204)" 
                        :input-border="false" 
                        :placeholder="$t('请填写收货人姓名')" 
                        :maxlength="20"
                        class="custom-input"
                    />
                </uni-forms-item>
                
                <uni-forms-item :label="$t('手机号')" name="mobile" required>
                    <uni-easyinput 
                        v-model="form.mobile" 
                        primary-color="rgb(192, 196, 204)" 
                        :input-border="false" 
                        :placeholder="$t('请填写收货人手机号')" 
                        :maxlength="11"
                        type="number"
                        class="custom-input"
                    />
                </uni-forms-item>
            </uni-forms>
        </view>

         <!-- 粘贴文本智能识别区域 -->
        <view class="paste-area">
            <view class="paste-content">
                <view class="input-section">
                    <textarea 
                        v-model="pasteText" 
                        class="paste-input" 
                        placeholder="请输入完整地址信息，必须包含：收货人姓名、手机号、省市区、详细地址。例如：张三 13255036666 安徽省合肥市包河区绿地中心写字楼A座1205室"
                        :maxlength="200"
                        auto-height
                    />
                    <view class="input-actions">
                        <view class="char-count">{{ pasteText.length }}/200</view>
                        <view class="action-buttons">
                            <!-- <view class="paste-btn" @click="handlePaste">粘贴</view> -->
                            <view 
                                class="smart-btn" 
                                :class="{ disabled: !isInputValid }"
                                @click="handleDirectRecognition"
                            >
                                智能识别
                            </view>
                        </view>
                    </view>
                </view>
                
                <!-- 实时校验提示 -->
                <view v-if="pasteText.trim() && missingInfo.length > 0" class="validation-tips">
                    <view class="tips-title">缺少必要信息：</view>
                    <view class="tips-list">
                        <view v-for="item in missingInfo" :key="item" class="tip-item">• {{ item }}</view>
                    </view>
                </view>
                
                <view v-if="pasteText.trim() && isInputValid" class="validation-success">
                    <view class="success-text">信息完整，可以进行智能识别</view>
                </view>
            </view>
        </view>

        <view class="address-edit-content">
            <uni-forms ref="formRef2" :model-value="form" label-width="140rpx">
                <uni-forms-item :label="$t('所在地区')" name="regionNames" required>
                    <view class="region-selector" @click="handleShowSelectRegion">
                        <text class="region-text">{{ form.regionNames || $t('点击选择所在地区') }}</text>
                        <view class="iconfont icon-xiangyou region-arrow"></view>
                    </view>
                </uni-forms-item>
                
                <uni-forms-item :label="$t('详细地址')" name="address" required>
                    <view class="address-input-wrapper">
                        <uni-easyinput
                            v-model="form.address"
                            primary-color="rgb(192, 196, 204)"
                            :input-border="false"
                            :placeholder="$t('请您填写详细收货地址')"
                            type="textarea"
                            :maxlength="100"
                            class="custom-input address-input"
                        />
                        <view class="char-count">{{ form.address.length }}/100</view>
                    </view>
                </uni-forms-item>
                
                <uni-forms-item :label="$t('固定电话')" name="telephone">
                    <uni-easyinput 
                        v-model="form.telephone" 
                        primary-color="rgb(192, 196, 204)" 
                        :input-border="false" 
                        :placeholder="$t('请输入固定电话')" 
                        :maxlength="20"
                        type="number"
                        class="custom-input"
                    />
                </uni-forms-item>
                
                <uni-forms-item :label="$t('设为默认')" name="isDefault">
                    <view class="switch-container">
                        <u-switch 
                            v-model="isDefault" 
                            active-color="#4A5568" 
                            inactive-color="#E5E5E5"
                            size="20" 
                            @change="onSwitchChange"
                        />
                    </view>
                </uni-forms-item>
            </uni-forms>
        </view>

        <!-- 底部按钮 -->
        <view class="fixed-bottom">
            <view class="save-btn" @click="onSubmit" :class="{ loading: isLoading }">
                <text v-if="isLoading">保存中...</text>
                <text v-else>保存地址</text>
            </view>
        </view>
    </view>
    
    <selectRegion v-model:show="showSelectRegion" v-model="form.regionIds" @send-region-names="getRegionNames" />
</template>

<script lang="ts" setup>
import selectRegion from "@/components/region/selectRegion.vue";
import { nextTick, reactive, ref, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getAddressData, updateAddressData, addAddressData } from "@/api/user/address";
import { getRegionByIds } from "@/api/region";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const title = ref("添加地址");

const id = ref(null);
const form = reactive({
    consignee: "",
    regionIds: [] as number[],
    address: "",
    mobile: "",
    telephone: "",
    email: "",
    regionNames: "",
    isDefault: 0
});

const isDefault = ref(false);
const pasteText = ref('');

// 实时校验输入是否有效
const isInputValid = computed(() => {
    if (!pasteText.value || pasteText.value.trim() === '') {
        return false;
    }
    const validationResult = validateAddressInput(pasteText.value);
    return validationResult.isValid;
});

// 实时获取缺失的信息
const missingInfo = computed(() => {
    if (!pasteText.value || pasteText.value.trim() === '') {
        return [];
    }
    const validationResult = validateAddressInput(pasteText.value);
    return validationResult.missingItems || [];
});

const rules = {
    consignee: {
        rules: [
            { required: true, errorMessage: t("请您填写收货人姓名") },
            { maxLength: 20, errorMessage: t("收货人姓名不能超过20个字符") }
        ]
    },
    regionNames: {
        rules: [
            { required: true, errorMessage: t("请选择所在地区") },
            {
                validateFunction: function (rule: any, value: any, data: any, callback: any) {
                    if (form.regionIds.length < 1) {
                        callback(t("请选择所在地区"));
                    }
                    return true;
                }
            }
        ]
    },
    address: {
        rules: [
            { required: true, errorMessage: t("请您填写详细收货地址") },
            { maxLength: 100, errorMessage: t("详细地址不能超过100个字符") }
        ]
    },
    mobile: {
        rules: [
            { required: true, errorMessage: t("请您填写收货人手机号码") },
            { minLength: 11, maxLength: 11, errorMessage: t("手机号码必须是11位数字") },
            {
                validateFunction: function (rule: any, value: any, data: any, callback: any) {
                    const regex = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
                    const status = regex.test(value);
                    if (!status) {
                        callback(t("请输入正确的手机号码"));
                    }
                    return true;
                }
            }
        ]
    },
    telephone: {
        rules: [
            { maxLength: 20, errorMessage: t("固定电话不能超过20个字符") },
            {
                validateFunction: function (rule: any, value: any, data: any, callback: any) {
                    // 固定电话格式验证（可选）
                    if (value && value.length > 0) {
                        const regex = /^(\d{3,4}-?)?\d{7,8}$/;
                        const status = regex.test(value);
                        if (!status) {
                            callback(t("请输入正确的固定电话格式"));
                        }
                    }
                    return true;
                }
            }
        ]
    }
};

onShow(() => {
    nextTick(() => {
        formRef.value.setRules(rules);
    });
});

onLoad((option: any) => {
    if (option && option.id) {
        title.value = "编辑地址";
        id.value = option.id;
        __getAddressData();
    }
});

const __getAddressData = async () => {
    try {
        const result = await getAddressData({ id: id.value });
        const { regionNames } = result;
        Object.assign(form, result);
        form.regionNames = regionNames.join(" ");
        isDefault.value = form.isDefault === 1;
    } catch (error) {
        console.error(error);
    }
};

const getRegionNames = (val: string[]) => {
    form.regionNames = val.join(" ");
};

const showSelectRegion = ref(false);
const handleShowSelectRegion = () => {
    showSelectRegion.value = true;
};

const onSwitchChange = (value: boolean) => {
    // 可以在这里添加额外的逻辑，比如触觉反馈等
    // #ifdef APP-PLUS
    if (value) {
        uni.vibrateShort();
    }
    // #endif
};

// 由于后端没有getAllRegionTree API，改用分级获取的方式
// 获取所有省份数据
const getAllProvinces = async (): Promise<any[]> => {
    try {
        const result = await getRegionByIds('') as any[];
        if (result && Array.isArray(result) && result.length > 0) {
            const provinces = result[0] as any[];
            return Array.isArray(provinces) ? provinces : [];
        }
        return [];
    } catch (error) {
        return [];
    }
};

// 获取指定省份下的城市数据
const getCitiesByProvince = async (provinceId: string): Promise<any[]> => {
    try {
        const result = await getRegionByIds(provinceId) as any[];
        if (result && Array.isArray(result) && result.length > 1) {
            const cities = result[1] as any[];
            return Array.isArray(cities) ? cities : [];
        }
        return [];
    } catch (error) {
        return [];
    }
};

// 获取指定省市下的区县数据
const getDistrictsByProvinceCity = async (provinceId: string, cityId: string): Promise<any[]> => {
    try {
        const result = await getRegionByIds(`${provinceId},${cityId}`) as any[];
        if (result && Array.isArray(result) && result.length > 2) {
            const districts = result[2] as any[];
            return Array.isArray(districts) ? districts : [];
        }
        return [];
    } catch (error) {
        return [];
    }
};

// 根据地区名称查找regionIds（分级获取模式）
const findRegionIdsByNames = async (regionNames: string[]) => {
    if (!regionNames.length) {
        return [];
    }
    
    // 标准化地区名称（去除常见后缀）
    const normalizeRegionName = (name: string) => {
        return name.replace(/省|市|区|县|自治区|特别行政区|地区|州|盟|自治州|自治县/g, '').trim();
    };
    
    const findRegionInList = (regions: any[], name: string) => {
        const normalizedName = normalizeRegionName(name);
        
        // 精确匹配
        for (const region of regions) {
            if (region.regionName === name) {
                return region;
            }
        }
        
        // 标准化名称匹配
        for (const region of regions) {
            const normalizedRegionName = normalizeRegionName(region.regionName);
            if (normalizedRegionName === normalizedName) {
                return region;
            }
        }
        
        // 包含匹配
        for (const region of regions) {
            if (region.regionName.includes(normalizedName) || normalizedName.includes(normalizeRegionName(region.regionName))) {
                return region;
            }
        }
        
        return null;
    };
    
    const regionIds = [];
    
    try {
        // 第一步：匹配省份
        if (regionNames[0]) {
            const provinces = await getAllProvinces();
            const foundProvince = provinces ? findRegionInList(provinces, regionNames[0]) : null;
            
            if (foundProvince) {
                regionIds.push(foundProvince.regionId);
                
                // 第二步：匹配城市
                if (regionNames[1]) {
                    const cities = await getCitiesByProvince(foundProvince.regionId);
                    const foundCity = cities ? findRegionInList(cities, regionNames[1]) : null;
                    
                    if (foundCity) {
                        regionIds.push(foundCity.regionId);
                        
                        // 第三步：匹配区县
                        if (regionNames[2]) {
                            const districts = await getDistrictsByProvinceCity(foundProvince.regionId, foundCity.regionId);
                            const foundDistrict = districts ? findRegionInList(districts, regionNames[2]) : null;
                            
                            if (foundDistrict) {
                                regionIds.push(foundDistrict.regionId);
                            }
                        }
                    }
                }
            }
        }
    } catch (error) {
        // 地区匹配失败，返回空数组
    }
    
    return regionIds;
};

// 粘贴功能 - 已注释
/*
const handlePaste = async () => {
    try {
        // #ifdef H5
        const text = await navigator.clipboard.readText();
        pasteText.value = text;
        uni.showToast({
            title: '粘贴成功',
            icon: 'success'
        });
        // #endif
        
        // #ifdef APP-PLUS
        uni.getClipboardData({
            success: (res) => {
                pasteText.value = res.data;
                uni.showToast({
                    title: '粘贴成功',
                    icon: 'success'
                });
            },
            fail: () => {
                uni.showToast({
                    title: '获取剪贴板内容失败',
                    icon: 'none'
                });
            }
        });
        // #endif
        
        // #ifdef MP
        uni.showModal({
            title: '提示',
            content: '小程序无法直接访问剪贴板，请手动输入地址后点击智能识别',
            showCancel: false
        });
        // #endif
    } catch (error) {
        console.error('粘贴失败:', error);
        uni.showToast({
            title: '粘贴失败，请手动输入',
            icon: 'none'
        });
    }
};
*/



// 校验地址输入是否包含必要信息
const validateAddressInput = (text: string) => {
    const cleanText = text.trim().replace(/\s+/g, ' ');
    
    // 校验手机号
    const phoneRegex = /1[3-9]\d{9}/g;
    const hasPhone = phoneRegex.test(cleanText);
    
    // 校验姓名（中文姓名，2-4个字符）
    const nameRegex = /(?:收货人|姓名|联系人)?[：:\s]*([^\d\s]{2,4}(?:先生|女士|小姐)?)/;
    const hasName = nameRegex.test(cleanText);
    
    // 校验是否包含地区信息
    const regionRegex = /([\u4e00-\u9fa5]{2,}(?:省|市|区|县|自治区|特别行政区|地区|州|盟))/;
    const hasRegion = regionRegex.test(cleanText);
    
    // 校验是否包含详细地址（去除姓名、手机号、地区后还有内容）
    let detailCheck = cleanText;
    const phoneMatches = cleanText.match(phoneRegex);
    if (phoneMatches) {
        phoneMatches.forEach(phone => {
            detailCheck = detailCheck.replace(phone, '');
        });
    }
    const nameMatch = cleanText.match(nameRegex);
    if (nameMatch) {
        detailCheck = detailCheck.replace(nameMatch[0], '');
    }
    const regionMatches = cleanText.match(new RegExp(regionRegex.source, 'g'));
    if (regionMatches) {
        regionMatches.forEach(region => {
            detailCheck = detailCheck.replace(region, '');
        });
    }
    
    detailCheck = detailCheck
        .replace(/收货人|姓名|联系人|地址|详细地址/g, '')
        .replace(/[：:\s]+/g, '')
        .trim();
    
    const hasDetailAddress = detailCheck.length >= 5; // 详细地址至少5个字符
    
    // 构建错误信息
    const missingItems = [];
    if (!hasName) missingItems.push('收货人姓名');
    if (!hasPhone) missingItems.push('手机号码');
    if (!hasRegion) missingItems.push('省市区信息');
    if (!hasDetailAddress) missingItems.push('详细地址');
    
    const isValid = hasName && hasPhone && hasRegion && hasDetailAddress;
    
    let message = '';
    if (!isValid) {
        message = `请确保输入内容包含以下信息：\n${missingItems.map(item => `• ${item}`).join('\n')}\n\n正确格式示例：\n张三 13812345678 北京市朝阳区望京街道某某小区1号楼2单元303室`;
    }
    
    return {
        isValid,
        message,
        missingItems,
        hasName,
        hasPhone,
        hasRegion,
        hasDetailAddress
    };
};

// 直接识别输入框中的文字
const handleDirectRecognition = async () => {
    if (!pasteText.value || pasteText.value.trim() === '') {
        uni.showToast({
            title: '请先输入地址信息',
            icon: 'none'
        });
        return;
    }
    
    // 校验输入内容是否包含必要信息
    const validationResult = validateAddressInput(pasteText.value);
    if (!validationResult.isValid) {
        uni.showModal({
            title: '输入信息不完整',
            content: validationResult.message,
            showCancel: false,
            confirmText: '我知道了'
        });
        return;
    }
    
    // 如果按钮被禁用，不执行识别
    if (!isInputValid.value) {
        uni.showToast({
            title: '请完善必要信息后再进行识别',
            icon: 'none'
        });
        return;
    }
    
    await parseAddress(pasteText.value);
};



// 地址解析函数
const parseAddress = async (addressText: string) => {
    if (!addressText || addressText.trim() === '') {
        uni.showToast({
            title: '地址内容为空',
            icon: 'none'
        });
        return;
    }

    uni.showLoading({
        title: '正在解析地址...'
    });

    try {
        const result = smartParseAddress(addressText);
        
        if (result.success) {
            // 填充解析结果到表单
            if (result.name) {
                form.consignee = result.name;
            }
            if (result.phone) {
                form.mobile = result.phone;
            }
            if (result.regions && result.regions.length > 0) {
                form.regionNames = result.regions.join(' ');
                
                // 根据地区名称获取对应的regionIds
                try {
                    uni.showLoading({
                        title: '正在匹配地区...'
                    });
                    const regionIds = await findRegionIdsByNames(result.regions);
                    
                    if (regionIds.length > 0) {
                        form.regionIds = regionIds;
                        uni.showToast({
                            title: `成功匹配${regionIds.length}级地区`,
                            icon: 'success',
                            duration: 1500
                        });
                    } else {
                        uni.showToast({
                            title: '未能自动匹配地区，请手动选择',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                } catch (error) {
                    uni.showToast({
                        title: '地区匹配失败，请手动选择',
                        icon: 'none',
                        duration: 2000
                    });
                }
            }
            if (result.detail) {
                form.address = result.detail;
            }
            
            // 清空输入框
            pasteText.value = '';
            
            uni.hideLoading();
            uni.showToast({
                title: '地址解析成功',
                icon: 'success'
            });
        } else {
            uni.hideLoading();
            uni.showToast({
                title: result.message || '地址解析失败',
                icon: 'none'
            });
        }
    } catch (error) {
        uni.hideLoading();
        uni.showToast({
            title: '地址解析失败',
            icon: 'none'
        });
    }
};

// 智能地址解析算法
const smartParseAddress = (text: string) => {
    const cleanText = text.trim().replace(/\s+/g, ' ');
    
    // 手机号正则
    const phoneRegex = /1[3-9]\d{9}/g;
    const phones = cleanText.match(phoneRegex);
    
    // 姓名正则（通常在地址开头或手机号前后）
    const nameRegex = /(?:收货人|姓名|联系人)?[：:\s]*([^\d\s]{2,4}(?:先生|女士|小姐)?)/;
    const nameMatch = cleanText.match(nameRegex);
    
    // 分别匹配省、市、区
    const provinceRegex = /([\u4e00-\u9fa5]{2,}(?:省|自治区|特别行政区))/;
    const cityRegex = /([\u4e00-\u9fa5]{2,}(?:市|地区|州|盟))/;
    const districtRegex = /([\u4e00-\u9fa5]{2,}(?:区|县|市))/;
    
    const provinceMatch = cleanText.match(provinceRegex);
    const cityMatch = cleanText.match(cityRegex);
    const districtMatch = cleanText.match(districtRegex);
    
    // 详细地址（去除已识别的省市区、姓名、手机号后的剩余部分）
    let detailAddress = cleanText;
    if (phones) {
        phones.forEach(phone => {
            detailAddress = detailAddress.replace(phone, '');
        });
    }
    if (nameMatch) {
        detailAddress = detailAddress.replace(nameMatch[0], '');
    }
    // 移除已识别的省市区
    if (provinceMatch) {
        detailAddress = detailAddress.replace(provinceMatch[0], '');
    }
    if (cityMatch) {
        detailAddress = detailAddress.replace(cityMatch[0], '');
    }
    if (districtMatch) {
        detailAddress = detailAddress.replace(districtMatch[0], '');
    }
    
    // 清理详细地址
    detailAddress = detailAddress
        .replace(/收货人|姓名|联系人|地址|详细地址/g, '')
        .replace(/[：:\s]+/g, '')
        .trim();
    
    // 构建结果
    const result: any = {
        success: false,
        message: '',
        name: '',
        phone: '',
        regions: [],
        detail: ''
    };
    
    // 提取姓名
    if (nameMatch && nameMatch[1]) {
        result.name = nameMatch[1].replace(/先生|女士|小姐/, '');
    }
    
    // 提取手机号
    if (phones && phones.length > 0) {
        result.phone = phones[0];
    }
    
    // 提取省市区
    const regions = [];
    if (provinceMatch && provinceMatch[1]) regions.push(provinceMatch[1]);
    if (cityMatch && cityMatch[1]) regions.push(cityMatch[1]);
    if (districtMatch && districtMatch[1]) regions.push(districtMatch[1]);
    result.regions = regions;
    
    // 提取详细地址
    if (detailAddress) {
        result.detail = detailAddress;
    }
    
    // 判断解析是否成功
    if (result.regions.length > 0 || result.detail || result.name || result.phone) {
        result.success = true;
        result.message = '地址解析成功';
    } else {
        result.message = '未能识别有效的地址信息';
    }
    
    return result;
};

const goBack = () => {
    // #ifdef H5
    if (window.history.length > 1) {
        window.history.go(-1);
    } else {
        uni.navigateTo({
            url: '/pages/address/list'
        });
    }
    // #endif
    // #ifndef H5
    uni.navigateBack({
        delta: 1,
        fail: (err) => {
            console.error('返回失败:', err);
            uni.navigateTo({
                url: '/pages/address/list'
            });
        }
    });
    // #endif
};

const isLoading = ref(false);
const formRef = ref();
const onSubmit = () => {
    formRef.value
        .validate()
        .then(() => {
            form.isDefault = isDefault.value ? 1 : 0;
            // if (isLoading.value) return;
            // isLoading.value = true;
            if (id.value) {
                edit();
            } else {
                add();
            }
        })
        .catch((err: any) => {
            console.log("表单错误信息：", err);
        });
};

const add = async () => {
    isLoading.value = true;
    try {
        await addAddressData(form);
        uni.showToast({
            title: t("添加地址成功"),
            icon: "none",
            duration: 1000
        });

        setTimeout(() => {
            uni.navigateBack({
                success: function (res) {
                    uni.$emit("refreshData"); // 发送刷新信号
                    isLoading.value = false
                }
            });
        }, 1000);
    } catch (error: any) {
        console.error(error);
        uni.showToast({
            title: error.message,
            icon: "none",
            duration: 1000
        });
        isLoading.value = false
    }
};

const edit = async () => {
    isLoading.value = true;
    try {
        await updateAddressData({ id: id.value, ...form });
        uni.showToast({
            title: t("编辑地址成功"),
            icon: "none",
            duration: 1000
        });

        setTimeout(() => {
            uni.navigateBack({
                success: function (res) {
                    uni.$emit("refreshData"); // 发送刷新信号
                    isLoading.value = false
                }
            });
        }, 1000);
    } catch (error: any) {
        console.error(error);
        uni.showToast({
            title: error.message,
            icon: "none",
            duration: 1000
        });
    }
};
</script>

<style lang="scss" scoped>
/* 自定义标题栏 */
.custom-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 140rpx;
    background: #fff;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 150rpx 50rpx 30rpx;
    z-index: 999;
    
    .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .back-icon {
            width: 40rpx;
            height: 40rpx;
        }
    }
    
    .header-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
        text-align: center;
    }
    
    .header-placeholder {
        width: 60rpx;
        height: 60rpx;
    }
}

.address-edit-main {
    min-height: 100vh;
    background: #F6F6F6;
    padding-top: 210rpx;
    padding-bottom: 140rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;

    .address-edit-content {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 24rpx 24rpx;
        margin-bottom: 16rpx;
    }
}

/* 粘贴文本智能识别区域 */
.paste-area {
    margin-bottom: 32rpx;
    
    .paste-content {
        
        .input-section {
            position: relative;
            
            .paste-input {
                width: 100%;
                min-height: 200rpx;
                background: #fff;
                border: 1rpx solid #E5E5E5;
                border-radius: 16rpx;
                padding: 32rpx 24rpx 80rpx 24rpx;
                font-size: 26rpx;
                color: #333;
                line-height: 1.6;
                resize: none;
                box-sizing: border-box;
                transition: border-color 0.3s ease, box-shadow 0.3s ease;

                &::placeholder {
                    color: #999;
                    font-size: 26rpx;
                    line-height: 1.5;
                }

                &:focus {
                    border-color: #4A5568;
                    outline: none;
                    box-shadow: 0 0 0 2rpx rgba(74, 85, 104, 0.1);
                }
            }
            
            .input-actions {
                position: absolute;
                bottom: 20rpx;
                right: 20rpx;
                display: flex;
                align-items: center;
                gap: 20rpx;
                background: rgba(255, 255, 255, 0.95);
                padding: 14rpx 20rpx;
                border-radius: 12rpx;
                border: 1rpx solid #E5E5E5;
                backdrop-filter: blur(4rpx);
                
                .char-count {
                    font-size: 24rpx;
                    color: #999;
                    white-space: nowrap;
                }
                
                .action-buttons {
                    display: flex;
                    gap: 12rpx;

                    .paste-btn, .smart-btn {
                        padding: 12rpx 20rpx;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        text-align: center;
                        white-space: nowrap;

                        &:active {
                            opacity: 0.7;
                            transform: scale(0.95);
                        }
                    }
                    
                    /* 粘贴按钮样式 - 已注释
                    .paste-btn {
                        background: #F5F5F5;
                        color: #666;
                        border: 1rpx solid #E0E0E0;
                        font-weight: 400;
                        
                        &:hover {
                            background: #E8F5E8;
                            color: #2E7D2E;
                        }
                    }
                    */
                    
                    .smart-btn {
                        background: #4A5568;
                        color: #fff;
                        font-weight: 500;
                        box-shadow: 0 2rpx 6rpx rgba(74, 85, 104, 0.2);
                        transition: all 0.3s ease;

                        &:hover {
                            background: #2D3748;
                            transform: translateY(-1rpx);
                            box-shadow: 0 4rpx 12rpx rgba(74, 85, 104, 0.3);
                        }

                        &:active {
                            background: #1A202C;
                            transform: translateY(0);
                            box-shadow: 0 2rpx 6rpx rgba(74, 85, 104, 0.2);
                        }

                        &.disabled {
                            background: #CBD5E0;
                            color: #A0AEC0;
                            cursor: not-allowed;
                            box-shadow: none;

                            &:hover,
                            &:active {
                                background: #CBD5E0;
                                color: #A0AEC0;
                                transform: none;
                                opacity: 1;
                            }
                        }
                    }
                }
            }
        }
    }
}

/* 校验提示区域 */
.validation-tips {
    margin-top: 20rpx;
    padding: 20rpx;
    background: #FFF3CD;
    border: 1rpx solid #FFE4A3;
    border-radius: 8rpx;
    
    .tips-title {
        font-size: 26rpx;
        color: #856404;
        font-weight: 500;
        margin-bottom: 12rpx;
    }
    
    .tips-list {
        .tip-item {
            font-size: 24rpx;
            color: #856404;
            line-height: 1.4;
            margin-bottom: 4rpx;
        }
    }
}

.validation-success {
    margin-top: 20rpx;
    padding: 20rpx;
    background: #D4EDDA;
    border: 1rpx solid #C3E6CB;
    border-radius: 8rpx;
    
    .success-text {
        font-size: 26rpx;
        color: #155724;
        font-weight: 500;
    }
}

/* 地区选择器 */
.region-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60rpx;
    cursor: pointer;
    
    .region-text {
        font-size: 28rpx;
        color: #333;
        flex: 1;
        
        &.placeholder {
            color: #999;
        }
    }
    
    .region-arrow {
        font-size: 24rpx;
        color: #999;
        margin-left: 12rpx;
    }
}

/* 开关容器 */
.switch-container {
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    transition: all 0.3s ease;
    
    :deep(.u-switch) {
        transition: all 0.3s ease;
        
        &.u-switch--active {
            transform: scale(1.05);
        }
        
        &:active {
            transform: scale(0.95);
        }
    }
    
    :deep(.u-switch__node) {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
        
        &.u-switch__node--active {
            box-shadow: 0 4rpx 12rpx rgba(74, 85, 104, 0.3);
        }
    }
    
    :deep(.u-switch__bg) {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
        &.u-switch__bg--active {
            background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
        }
        
        &:not(.u-switch__bg--active) {
            background: #E5E5E5;
            border: 1rpx solid #D1D5DB;
        }
    }
}

/* 底部固定按钮 */
.fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 30rpx 40rpx;
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #E5E5E5;
    
    .save-btn {
        width: 100%;
        height: 88rpx;
        background: #4A5568;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:active {
            opacity: 0.8;
            transform: scale(0.98);
        }
        
        &.loading {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        text {
            font-size: 32rpx;
            color: #fff;
        }
    }
}

/* 表单相关样式覆盖 */
:deep(.uni-forms-item) {
    margin-bottom: 32rpx;
    border-bottom: 1rpx solid #F0F0F0;
    padding-bottom: 16rpx;
    
    &:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
}

:deep(.uni-forms-item__error) {
    top: 100%;
    left: 0;
    font-size: 22rpx;
    color: #FF4757;
}

:deep(.uni-forms-item__label) {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    position: relative;
    width: 140rpx;
    
    .is-required {
        position: absolute;
        top: 0;
        left: -15rpx;
        color: #FF4757;
        font-size: 28rpx;
    }
}

:deep(.uni-easyinput__placeholder-class) {
    font-size: 26rpx;
    color: #999;
}

:deep(.uni-easyinput__content-input) {
    font-size: 28rpx;
    color: #333;
}

:deep(.custom-input .uni-easyinput__content) {
    background: transparent;
    border: none;
    padding: 0;
}

/* 详细地址输入框包装器 */
.address-input-wrapper {
    position: relative;
    
    .char-count {
        position: absolute;
        bottom: 8rpx;
        right: 12rpx;
        font-size: 22rpx;
        color: #999;
        background: rgba(255, 255, 255, 0.8);
        padding: 4rpx 8rpx;
        border-radius: 4rpx;
        z-index: 10;
    }
}

:deep(.address-input .uni-easyinput__content-textarea) {
    min-height: 80rpx;
    font-size: 28rpx;
    color: #333;
    padding-bottom: 40rpx; /* 为字符计数器预留空间 */
}

:deep(.is-disabled) {
    background-color: #fff;
}
</style>
