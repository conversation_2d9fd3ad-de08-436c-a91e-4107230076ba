<template>
    <!-- 自定义标题栏 -->
    <view class="custom-header">
        <view class="back-btn" @click="goBack">
            <image class="back-icon" src="/static/images/common/<EMAIL>" mode="aspectFit" />
        </view>
        <view class="header-title">店铺收藏</view>
        <view class="header-placeholder"></view>
    </view>

    <!-- 主要内容 -->
    <view class="collection-main">
        <view v-if="collectionShopList.length > 0" class="collection-shop">
            <uni-swipe-action>
                <view class="collection-content">
                    <template v-for="item in collectionShopList" :key="item.shopId">
                        <view class="move-item">
                            <uni-swipe-action-item :threshold="0" auto-close>
                                <view class="shop-item" @click="handleLink(item.shopId)">
                                    <view class="shop-item-left">
                                        <tig-image :src="item.shop.shopLogo" />
                                    </view>
                                    <view class="shop-item-right">
                                        <view class="shop-name line1">{{ item.shop.shopTitle }}</view>
                                        <view class="shop-desc line1">{{ item.shop.description ? item.shop.description : "" }}</view>
                                        <view class="shop-info">
                                            <text class="shop-info-text">上架商品：{{ item.productCount }}</text>
                                            <text class="shop-info-text">店铺收藏：{{ item.collectCount }}</text>
                                        </view>
                                    </view>
                                </view>
                                <template #right>
                                    <view class="collect-move-box">
                                        <view class="btn-del" @click="handleCancel(item.shopId)"
                                            ><text>{{ $t("取消收藏") }}</text></view
                                        >
                                    </view>
                                </template>
                            </uni-swipe-action-item>
                        </view>
                    </template>
                </view>
            </uni-swipe-action>
        </view>
        
        <!-- 空状态 -->
        <view v-if="collectionShopList.length === 0 && !loading && !loaded" class="empty-state">
            <view class="empty-title">暂无关注店铺</view>
            <view class="empty-subtitle">快去看看中意的店铺吧</view>
            <image class="empty-image" src="/static/images/common/<EMAIL>" mode="aspectFit" />
        </view>
        
        <!-- <loading-box v-model="loaded" :page="filterParams.page" :length="collectionShopList.length"></loading-box> -->
        <!-- <tig-loadingpage v-model="loading"></tig-loadingpage> -->
    </view>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { getCollectionShop } from "@/api/user/collectionShop";
import { shopCollection } from "@/api/shop/shop";
import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import type { collectionShopList } from "@/types/user/collectionShop";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const filterParams = reactive({
    //初使化用于查询的参数
    page: 1,
    size: 10,
    keyword: ""
});
const collectionShopList = ref<collectionShopList[]>([]);
const total = ref(0);
const loading = ref(false);
const loaded = ref(false);
const __getCollectionShop = async () => {
    if (filterParams.page > 1) {
        loaded.value = true;
    } else {
        loading.value = true;
    }
    try {
        const result = await getCollectionShop(filterParams);
        collectionShopList.value = result.records;
        total.value = result.total;
    } catch (error) {
        console.error(error);
    } finally {
        loaded.value = false;
        loading.value = false;
    }
};

onLoad(() => {
    __getCollectionShop();
});

const handleLink = (id: number) => {
    uni.navigateTo({
        url: `/pages/shop/index?shopId=${id}`
    });
};

const handleCancel = async (id: number) => {
    try {
        const result = await shopCollection({ shopId: id });
        uni.showToast({
            title: t("取消成功"),
            icon: "none"
        });
        filterParams.page = 1;
        collectionShopList.value = [];
        __getCollectionShop();
    } catch (error) {
        console.error(error);
    }
};

const goBack = () => {
    // #ifdef H5
    if (window.history.length > 1) {
        window.history.go(-1);
    } else {
        uni.switchTab({
            url: '/pages/user/index'
        });
    }
    // #endif
    // #ifndef H5
    uni.navigateBack({
        delta: 1,
        fail: (err) => {
            console.error('返回失败:', err);
            uni.switchTab({
                url: '/pages/user/index'
            });
        }
    });
    // #endif
};

onReachBottom(() => {
    if (filterParams.page < Math.ceil(total.value / filterParams.size)) {
        filterParams.page++;
        __getCollectionShop();
    }
});
</script>

<style lang="scss" scoped>
/* 自定义标题栏 */
.custom-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 250rpx;
    background: #fff;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 30rpx 30rpx;
    z-index: 999;
    
    .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .back-icon {
            width: 40rpx;
            height: 40rpx;
            position: relative;
            top: 4px;
        }
    }
    
    .header-title {
        font-size: 35rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
        text-align: center;
    }
    
    .header-placeholder {
        width: 60rpx;
        height: 60rpx;
    }
}

/* 主要内容区域 */
.collection-main {
    min-height: 100vh;
    background: #F6F6F6;
    padding-top: 260rpx;
}

.collection-content {
    padding: 25rpx;
    .move-item {
        margin-bottom: 20rpx;
    }
    .shop-item {
        padding: 30rpx 20rpx;
        background-color: #fff;
        border-radius: 20rpx;
        display: flex;
        .shop-item-left {
            width: 130rpx;
            height: 130rpx;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .shop-item-right {
            width: calc(100% - 150rpx);
            padding-left: 20rpx;
            .shop-name {
                font-size: 28rpx;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .shop-desc {
                font-size: 24rpx;
                color: #999;
                width: 100%;
                padding-top: 15rpx;
                height: 50rpx;
            }
            .shop-info {
                font-size: 24rpx;
                color: #999;
                padding-top: 10rpx;

                .shop-info-text {
                    &:first-child {
                        padding-right: 18rpx;
                    }
                }
            }
        }
    }
}

/* 空状态样式 */
.empty-state {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #F6F6F6;
    z-index: 1;
    padding: 0 60rpx;
    box-sizing: border-box;
    
    .empty-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;
        text-align: center;
    }
    
    .empty-subtitle {
        font-size: 25rpx;
        color: #999;
        margin-bottom: 80rpx;
        text-align: center;
        position: relative;
        top: -5px;
    }
    
    .empty-image {
        width: 360rpx;
        height: 280rpx;
    }
}

.collect-move-box {
    width: 150rpx;
    height: 100%;
    color: #fff;
    text-align: center;
    vertical-align: middle;
    display: flex;
    text {
        display: block;
        position: absolute;
        top: 50%;
        margin-top: -20rpx;
        font-size: 24rpx;
        text-align: center;
        width: 100%;
    }
    .btn-collect {
        width: 150rpx;
        background: var(--vice-bg);
        color: var(--general);
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        display: table-cell;
        position: relative;
    }
    .btn-del {
        width: 150rpx;
        background: var(--general);
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        display: table-cell;
        position: relative;
    }
}
</style>
