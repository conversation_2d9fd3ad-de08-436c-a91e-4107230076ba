<template>
    <!-- 自定义标题栏 -->
    <view class="custom-header">
        <view class="back-btn" @click="goBack">
            <image src="@/static/images/common/<EMAIL>" class="back-icon" />
        </view>
        <view class="header-title">个人资料</view>
        <view class="header-placeholder"></view>
    </view>
    
    <view class="profile-edit-main">
            <uni-forms ref="formRef" :model-value="form" label-width="300rpx">
                <view class="profile-edit-content">
                    <uni-forms-item :label="$t('用户ID')" name="username">
                        <uni-easyinput v-model="form.dimUsername" :input-border="false" disabled primary-color="rgb(192, 196, 204)" class="userid-input" />
                    </uni-forms-item>
                    <uni-forms-item :label="$t('昵称')" name="nickname">
                        <uni-easyinput
                            v-model="form.nickname"
                            :input-border="false"
                            :placeholder="$t('请输入昵称')"
                            primary-color="rgb(192, 196, 204)"
                            type="nickname"
                            class="nickname-input"
                            maxlength="10"
                        />
                    </uni-forms-item>
                    <uni-forms-item :label="$t('出生日期')" name="birthday">
                        <view class="el-input-id" @click="handleShowDatetime">
                            {{ form.birthday === "0000-00-00" ? $t("请选择出生日期") : form.birthday }}
                            <image src="@/static/images/common/<EMAIL>" class="arrow-icon" />
                        </view>
                    </uni-forms-item>
                </view>
                <view class="profile-edit-content">
                    <uni-forms-item :label="$t('登录密码')" @tap="goPages('/pages/user/security/password' + '?mobile=' + form.mobile, 'password')">
                        <view class="el-input-id">
                            {{ $t("修改") }}
                            <image src="@/static/images/common/<EMAIL>" class="arrow-icon" />
                        </view>
                    </uni-forms-item>
                    <uni-forms-item :label="$t('手机号码')" @tap="goPages('/pages/user/security/phone' + '?mobile=' + form.mobile)">
                        <view class="el-input-id">
                            {{ form.showMobile || $t("去绑定") }}
                            <image src="@/static/images/common/<EMAIL>" class="arrow-icon" />
                        </view>
                    </uni-forms-item>
                    <!-- #ifdef H5 -->
                    <template v-if="configStore.XClientType == 'wechat'">
                        <uni-forms-item :label="$t('公众号绑定')">
                            <template v-if="form.isBindWechat">
                                <view class="el-input-id" @click.stop="unbindWechatAct">
                                    {{ $t("解绑") }}
                                    <image src="@/static/images/common/<EMAIL>" class="arrow-icon" />
                                </view>
                            </template>
                            <template v-else>
                                <view class="el-input-id" @click.stop="wechatLogin">
                                    {{ $t("绑定") }}
                                    <image src="@/static/images/common/<EMAIL>" class="arrow-icon" />
                                </view>
                            </template>
                        </uni-forms-item>
                    </template>
                    <!-- #endif -->
                </view>
            </uni-forms>
        </view>

        <view class="">
            <tig-copyright />
        </view>

        <tig-fixed-placeholder background-color="#fff" >
            <view class="btn-box">
                <view class="custom-btn logout-btn" @click="onLogout">
                    <text>{{ $t("退出登录") }}</text>
                </view>
                <view class="custom-btn save-btn" @click="onSubmit">
                    <text>{{ $t("保存修改") }}</text>
                </view>
            </view>
        </tig-fixed-placeholder>
        <up-datetime-picker
            ref="datetimePickerRef"
            :cancel-text="$t('取消')"
            :confirm-text="$t('确定')"
            :show="showDatetime"
            :model-value="Date.now()"
            mode="date"
            :formatter="formatter"
            format="YYYY-MM-DD"
            confirm-color="var(--general)"
            :min-date="1"
            @cancel="showDatetime = false"
            @confirm="getBirthday"
        />
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getProfile, updateProfile } from "@/api/user/profile";
import { useUserStore } from "@/store/user";
import { getOauthUrl, bindWechat, unbindWechat } from "@/api/login/login";
import { useConfigStore } from "@/store/config";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const userStore = useUserStore();

const configStore = useConfigStore();

const form = reactive<any>({
    nickname: "",
    birthday: "",
    mobile: ""
});

const goPages = (url: string, type?: string) => {
    if (type == "password" && form.mobile == "") {
        uni.showToast({
            title: t("未绑定手机号，请先绑定手机号再修改密码"),
            icon: "none"
        });
        return;
    }
    uni.navigateTo({
        url
    });
};

const rules = {
    nickname: {
        rules: [{ required: true, errorMessage: t("请您填写用户昵称") }]
    }
};

const formRef = ref();

const onSubmit = (values: any) => {
    formRef.value
        .validate()
        .then(() => {
            edit();
        })
        .catch((err: any) => {
            console.log("表单错误信息：", err);
        });
};
const onLogout = () => {
    uni.showModal({
        title: t("提示"),
        content: t("确定要退出登录吗？"),
        confirmText: t("退出"),
        cancelText: t("取消"),
        success: (res) => {
            if (res.confirm) {
                uni.showLoading({
                    title: t("正在退出...")
                });
                setTimeout(() => {
                    userStore.logout();
                    uni.hideLoading();
                    uni.showToast({
                        title: t("已退出登录"),
                        icon: "success",
                        duration: 1500
                    });
                }, 500);
            }
        }
    });
};

const goBack = () => {
    console.log('返回按钮被点击');
    // #ifdef H5
    if (window.history.length > 1) {
        window.history.go(-1);
    } else {
        uni.reLaunch({
            url: '/pages/user/index'
        });
    }
    // #endif
    // #ifndef H5
    uni.navigateBack({
        delta: 1,
        fail: (err) => {
            console.error('返回失败:', err);
            uni.reLaunch({
                url: '/pages/user/index'
            });
        }
    });
    // #endif
};

const edit = async () => {
    try {
        await updateProfile({ ...form });

        uni.showToast({
            title: t("修改成功"),
            icon: "none",
            duration: 1000
        });

        setTimeout(() => {
            uni.navigateBack({
                success: function (res) {
                    uni.$emit("refreshData"); // 发送刷新信号
                }
            });
        }, 1000);
    } catch (error: any) {
        console.error(error);
        uni.showToast({
            title: error.message,
            icon: "none",
            duration: 1000
        });
    }
};

onShow(() => {
    nextTick(() => {
        formRef.value.setRules(rules);
        // 微信小程序需要用此写法
        datetimePickerRef.value.setFormatter(formatter);
    });
    if (configStore.XClientType === "wechat" && uni.getStorageSync("token")) {
        const params = getUlParams(location.href);
        if (params.code && params.code.length > 12) {
            //授权
            bindWechatAct(params.code);
        }
    }
});

//绑定微信
const bindWechatAct = async (code: string) => {
    uni.showLoading({
        title: t("授权中") + "..."
    });
    try {
        const result = await bindWechat({ code: code });
        uni.showToast({
            title: result.message,
            icon: "none",
            duration: 1000
        });
        setTimeout(() => {
            uni.redirectTo({ url: "/pages/user/profile/index" });
        }, 500);
    } catch (error: any) {
        uni.showToast({
            title: error.message,
            icon: "none",
            duration: 1000
        });
    } finally {
        uni.hideLoading();
    }
};
//解绑
const unbindWechatAct = async () => {
    uni.showModal({
        title: t("提示"),
        content: t("确定取消绑定吗？"),
        success: async (res) => {
            if (res.confirm) {
                uni.showLoading({
                    title: t("正在解绑...")
                });
                try {
                    const result = await unbindWechat();
                    uni.hideLoading();
                    uni.showToast({
                        title: result.message,
                        icon: "none",
                        duration: 1000
                    });
                    setTimeout(() => {
                        __getProfile();
                    }, 500);
                } catch (error: any) {
                    console.error(error);
                    uni.showToast({
                        title: error.message,
                        icon: "none",
                        duration: 1000
                    });
                } finally {
                    uni.hideLoading();
                }
            }
        }
    });
};

const getUlParams = (url: string) => {
    const params: AnyObject = {};
    const urlParams = url.split("?")[1];
    if (urlParams) {
        urlParams.split("&").forEach((item: string) => {
            const [key, value] = item.split("=");
            params[key] = value;
        });
    }
    return params;
};

onLoad(() => {
    __getProfile();
});

const __getProfile = async () => {
    uni.showLoading({
        title: t("加载中")
    });
    try {
        const result = await getProfile();
        let temp: any = result.mobile;
        result.showMobile = temp.replace(/(\d{2})\d*(\d{4})$/, "$1*****$2");
        Object.assign(form, result);
    } catch (error) {
        console.error(error);
    } finally {
        uni.hideLoading();
    }
};

const formatter = (type: string, value: string) => {
    if (type === "year") {
        return `${value}${t("年")}`;
    }
    if (type === "month") {
        return `${value}${t("月")}`;
    }
    if (type === "day") {
        return `${value}${t("日")}`;
    }
    return value;
};

const datetimePickerRef = ref();
const showDatetime = ref(false);
const handleShowDatetime = () => {
    showDatetime.value = true;
};
const getBirthday = (val: any) => {
    form.birthday = uni.$u.timeFormat(val.value, "yyyy-mm-dd");
    showDatetime.value = false;
};

//微信授权登录
const wechatLogin = async () => {
    const result = await getOauthUrl({ url: location.origin + location.pathname });
    window.location.href = result.url;
};
</script>

<style lang="scss" scoped>
/* 自定义标题栏样式 */
.custom-header {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 160rpx 24rpx 30rpx;
    background: #DEE9FF;
    position: relative;
    height: 140rpx;
    
    .back-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
        cursor: pointer;
    }
    
    .back-icon {
        width: 40rpx;
        height: 40rpx;
    }
    
    .header-title {
        position: absolute;
        left: 50%;
        bottom: 30rpx;
        transform: translateX(-50%);
        font-size: 36rpx;
        font-weight: 600;
        color: #000;
    }
    
    .header-placeholder {
        width: 48rpx;
        height: 48rpx;
    }
}

:deep(.uni-forms-item) {
    margin-bottom: 0;
    padding: 24rpx 0;
    position: relative;
    border-bottom: 1rpx solid #F0F0F0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

:deep(.uni-forms-item:last-child) {
    border-bottom: none;
}

:deep(.uni-forms-item__label) {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
    width: 200rpx;
    flex-shrink: 0;
    margin-left: 10px;
}

:deep(.uni-forms-item__content) {
    min-height: 36rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    text-align: right;
    min-width: 300rpx;
    padding-right: 40rpx;
}

:deep(.uni-forms-item__error) {
    top: 90%;
    left: 18rpx;
    font-size: 22rpx;
    color: #FF4757;
}

:deep(.uni-input-input) {
    text-align: right;
    font-size: 26rpx;
    color: #999;
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    padding: 8rpx 0;
}

:deep(.uni-easyinput__placeholder-class) {
    font-size: 26rpx;
    text-align: right;
    color: #999;
}

:deep(.uni-easyinput) {
    width: 100%;
    text-align: right;
    background: transparent;
    border: none;
}

:deep(.uni-easyinput__content) {
    background: transparent;
    border: none;
    padding: 0;
}

:deep(.uni-easyinput__content-input) {
    background: transparent;
    border: none;
    text-align: right;
    color: #999;
    font-size: 26rpx;
}

/* 昵称输入框特殊样式 - 黑色字体 */
:deep(.uni-forms-item[name="nickname"] .uni-easyinput__content-input) {
    color: #333 !important;
}

:deep(.uni-forms-item[name="nickname"] .uni-input-input) {
    color: #333 !important;
}

:deep(.uni-forms-item[name="nickname"] .uni-easyinput) {
    color: #333 !important;
}

:deep(.uni-forms-item[name="nickname"] input) {
    color: #333 !important;
}

:deep(.uni-forms-item[name="nickname"] .uni-easyinput__content) {
    color: #333 !important;
}

/* 通过class选择器控制昵称输入框颜色 */
:deep(.nickname-input .uni-easyinput__content-input) {
    color: #333 !important;
}

:deep(.nickname-input .uni-input-input) {
    color: #333 !important;
}

:deep(.nickname-input input) {
    color: #333 !important;
}

/* 用户ID输入框样式 - 灰色字体 */
:deep(.userid-input .uni-easyinput__content-input) {
    color: #999 !important;
}

:deep(.userid-input .uni-input-input) {
    color: #999 !important;
}

:deep(.userid-input input) {
    color: #999 !important;
}

:deep(.userid-input .uni-easyinput__content) {
    color: #999 !important;
}

:deep(.is-disabled) {
    background-color: #fff !important;
}

:deep(.icon-calendar) {
    display: none;
}

:deep(.uni-date__x-input) {
    text-align: right;
    color: #999;
    font-size: 26rpx;
}

/* 自定义输入项样式 */
.el-input-id {
    height: 100%;
    color: #999;
    font-size: 26rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    min-width: 300rpx;
    padding: 8rpx 0;
    background: transparent;
    border: none;
    cursor: pointer;
    
    .iconfont {
        margin-left: 12rpx;
        color: #999;
        font-size: 24rpx;
    }
    
    .arrow-icon {
        width: 26rpx;
        height: 26rpx;
        margin-left: 15rpx;
        opacity: 0.6;
    }
}

.copyright-box {
    width: 100%;
    position: fixed;
    bottom: calc(120rpx + var(--safe-bottom));
    left: 0;
}

.profile-edit-main {
    padding: 30rpx;
    min-height: vh;
    background: linear-gradient(180deg, #DEE9FF 0%, #DEE9FF 30%, #F6F6F6 100%);

    .profile-edit-content {
        background-color: #fff;
        border-radius: 10rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;


    }

    :deep(.uni-calendar-item__weeks-box .uni-calendar-item--checked) {
        background-color: var(--general);
    }
    :deep(.uni-datetime-picker--btn) {
        background-color: var(--general);
    }
}
.birthday-input {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: end;
    height: 100%;
    font-size: 23rpx;
    color: #999;
}

.btn-box {
    padding: 25rpx 15rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    gap: 20rpx;

    .custom-btn {
        width: 48%;
        height: 88rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        
        text {
            font-size: 28rpx;
            font-weight: 500;
        }
        
        &:active {
            opacity: 0.8;
            transform: scale(0.98);
        }
    }
    
    .logout-btn {
        width: 35%;
        background: #fff;
        color: #333;
        border: 2rpx solid #E5E5E5;
        
        text {
            color: #333;
        }
    }
    
    .save-btn {
        width: 65%;
        background: #4A5568;
        color: #fff;
        border: none;
        
        text {
            color: #fff;
        }
    }
}
</style>
