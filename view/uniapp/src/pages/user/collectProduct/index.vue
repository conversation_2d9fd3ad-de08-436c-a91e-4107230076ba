<template>
    <!-- 自定义标题栏 -->
    <view class="custom-header">
        <view class="back-btn" @click="goBack">
            <image class="back-icon" src="/static/images/common/<EMAIL>" mode="aspectFit" />
        </view>
        <view class="header-title">商品收藏</view>
        <view class="header-right">
            <text v-if="collectList.length > 0" class="manage-btn" @click="toggleManageMode">{{ isManageMode ? '退出管理' : '管理' }}</text>
        </view>
    </view>

    <!-- 主要内容 -->
    <view class="collect-main">
        <view v-if="collectList.length > 0" class="collect-product">
            <view v-if="!isManageMode" class="normal-mode">
                <uni-swipe-action>
                    <block v-for="(item, index) in collectList" :key="item.productId">
                        <view class="move-item">
                            <uni-swipe-action-item 
                                :threshold="60" 
                                :auto-close="true"
                                :disabled="false"
                                @click="handleSwipeClick($event, item.productId)"
                            >
                                <view class="collect-product-item" @click="handleLink(item.productId)">
                                    <view class="collect-product-item-left">
                                        <view class="img-box">
                                            <tig-image :src="item.picThumb" />
                                        </view>
                                    </view>
                                    <view class="collect-product-item-right">
                                        <view class="shop-name">SANKUWA官方旗舰店</view>
                                        <view class="title">{{ item.productName }}</view>
                                        <view class="item-bottom">
                                            <view class="price">
                                                <format-price
                                                    :decimals-style="{
                                                        fontSize: '25rpx'
                                                    }"
                                                    :currency-style="{
                                                        fontSize: '23rpx'
                                                    }"
                                                    :price-data="item.productPrice"
                                                />
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <template #right>
                                    <view class="collect-move-box">
                                        <view 
                                            class="btn-del" 
                                            @tap="handleDeleteTap(item.productId)"
                                        >
                                            <text>{{ $t("取消收藏") }}</text>
                                        </view>
                                    </view>
                                </template>
                            </uni-swipe-action-item>
                            <!-- 下划线分割 -->
                            <view v-if="index < collectList.length - 1" class="divider"></view>
                        </view>
                    </block>
                </uni-swipe-action>
            </view>
            
            <!-- 管理模式 -->
            <view v-if="isManageMode" class="manage-mode">
                <view v-for="(item, index) in collectList" :key="item.productId" class="manage-item">
                    <!-- 选择框 -->
                    <view class="select-checkbox" @click="toggleSelect(item.productId)">
                        <view class="checkbox" :class="{ selected: selectedItems.includes(item.productId) }">
                            <view v-if="selectedItems.includes(item.productId)" class="checkbox-inner"></view>
                        </view>
                    </view>
                    <view class="collect-product-item">
                        <view class="collect-product-item-left">
                            <view class="img-box">
                                <tig-image :src="item.picThumb" />
                            </view>
                        </view>
                        <view class="collect-product-item-right">
                            <view class="shop-name">SANKUWA官方旗舰店</view>
                            <view class="title">{{ item.productName }}</view>
                            <view class="item-bottom">
                                <view class="price">
                                    <format-price
                                        :decimals-style="{
                                            fontSize: '25rpx'
                                        }"
                                        :currency-style="{
                                            fontSize: '23rpx'
                                        }"
                                        :price-data="item.productPrice"
                                    />
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 下划线分割 -->
                    <view v-if="index < collectList.length - 1" class="divider"></view>
                </view>
            </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="collectList.length === 0 && loadend === true" class="empty-state">
            <view class="empty-title">暂无收藏商品</view>
            <view class="empty-subtitle">快去看看喜欢的宝贝吧</view>
            <image class="empty-image" src="/static/images/common/<EMAIL>" mode="aspectFit" />
        </view>
        
        <loading-box v-model="loaded" :page="filterParams.page" :length="collectList.length" />
    </view>
    
    <!-- 管理模式底部操作栏 -->
    <view v-if="isManageMode" class="bottom-action-bar">
        <view class="select-all-wrapper">
            <view class="select-all-checkbox" @click="toggleSelectAll">
                <view class="checkbox" :class="{ selected: isAllSelected }">
                    <view v-if="isAllSelected" class="checkbox-inner"></view>
                </view>
                <text class="select-all-text">全选</text>
            </view>
        </view>
        <view class="action-btn" :class="{ disabled: selectedItems.length === 0 }" @click="batchDelete">
            取消收藏
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from "vue";
import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import { getCollectProductList, delCollectProduct } from "@/api/user/collectProduct";
import type { CollectProductList, CollectProductFilterParams } from "@/types/user/collectProduct";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const filterParams = reactive<CollectProductFilterParams>({
    //初使化用于查询的参数
    page: 1,
    size: 10,
    keyword: ""
});
const total = ref(0);
const loaded = ref(false);
const loadend = ref(false);
const collectList = ref<CollectProductList[]>([]);

// 管理模式相关
const isManageMode = ref(false);
const selectedItems = ref<number[]>([]);

// 计算属性：是否全选
const isAllSelected = computed(() => {
    return collectList.value.length > 0 && selectedItems.value.length === collectList.value.length;
});
const __getCollectProductList = async () => {
    if (filterParams.page > 1) {
        loaded.value = true;
    } else {
        uni.showLoading({
            title: t("加载中")
        });
    }
    try {
        const result = await getCollectProductList({ ...filterParams });
        total.value = result.total;
        collectList.value = [...collectList.value, ...result.records];
        // collectList.value = Object.assign(collectList.value, result.records)
    } catch (error) {
        console.error(error);
    } finally {
        uni.hideLoading();
        loaded.value = false;
        loadend.value = true;
    }
};

// 静默加载函数，不显示loading提示
const __getCollectProductListSilent = async () => {
    try {
        const result = await getCollectProductList({ ...filterParams });
        total.value = result.total;
        collectList.value = [...collectList.value, ...result.records];
        loadend.value = true;
    } catch (error) {
        console.error(error);
    }
};

const handleLink = (id: number) => {
    uni.redirectTo({
        url: "/pages/product/index?id=" + id
    });
};

const __delCollect = (id: number) => {
    // 设置删除状态，防止重复操作
    isDeleting.value = true;
    
    // 强制隐藏所有可能的loading，确保modal能正常显示
    try {
        uni.hideLoading();
    } catch (error) {
        console.log('隐藏loading失败:', error);
    }
    
    // 稍微延迟一下再显示modal，确保loading完全隐藏
    setTimeout(() => {
        uni.showModal({
            title: t("提示"),
            content: t("确定取消收藏吗？"),
            success: async (res) => {
                if (res.confirm) {
                    await deleteASiteCollection(id);
                } else {
                    // 用户取消时重置状态
                    isDeleting.value = false;
                    console.log('用户取消删除，重置状态');
                }
            },
            fail: () => {
                // modal显示失败时重置状态
                isDeleting.value = false;
                console.log('modal显示失败，重置状态');
            }
        });
    }, 100);
};

// 防重复点击标识
const isDeleting = ref(false);

// 处理滑动操作的点击事件
const handleSwipeClick = (event: any, productId: number) => {
    console.log('滑动组件点击事件:', event, productId);
    // 可以在这里添加额外的逻辑
};

// 处理删除按钮tap事件 - 统一事件处理
const handleDeleteTap = (productId: number) => {
    console.log('删除按钮tap事件触发:', productId);
    
    // 防止重复点击
    if (isDeleting.value) {
        console.log('正在删除中，忽略重复点击');
        return;
    }
    
    // 确保先隐藏任何可能存在的loading
    uni.hideLoading();
    __delCollect(productId);
};

const deleteASiteCollection = async (value: number) => {
    try {
        console.log('开始删除收藏商品:', value);
        
        // 显示加载提示
        uni.showLoading({
            title: t("处理中...")
        });
        
        await delCollectProduct({ id: value });
        
        // 先隐藏loading再显示成功提示
        uni.hideLoading();
        uni.showToast({
            title: t("取消收藏成功"),
            icon: "none"
        });

        // 重新加载列表 - 不显示loading因为用户已经看到了操作成功的提示
        filterParams.page = 1;
        collectList.value = [];
        await __getCollectProductListSilent(); // 使用静默加载
        
        console.log('删除收藏商品成功:', value);
    } catch (error: any) {
        uni.hideLoading();
        console.error('删除收藏商品失败:', error);
        uni.showToast({
            title: error.message || t("操作失败，请重试"),
            icon: "none",
            duration: 2000
        });
    } finally {
        // 无论成功还是失败，都要重置删除状态
        isDeleting.value = false;
        console.log('删除操作完成，重置状态');
    }
};

const goBack = () => {
    // #ifdef H5
    if (window.history.length > 1) {
        window.history.go(-1);
    } else {
        uni.switchTab({
            url: '/pages/user/index'
        });
    }
    // #endif
    // #ifndef H5
    uni.navigateBack({
        delta: 1,
        fail: (err) => {
            console.error('返回失败:', err);
            uni.switchTab({
                url: '/pages/user/index'
            });
        }
    });
    // #endif
};

// 切换管理模式
const toggleManageMode = () => {
    isManageMode.value = !isManageMode.value;
    if (!isManageMode.value) {
        selectedItems.value = [];
    }
};

// 切换单个商品选择状态
const toggleSelect = (productId: number) => {
    const index = selectedItems.value.indexOf(productId);
    if (index > -1) {
        selectedItems.value.splice(index, 1);
    } else {
        selectedItems.value.push(productId);
    }
};

// 切换全选状态
const toggleSelectAll = () => {
    if (isAllSelected.value) {
        selectedItems.value = [];
    } else {
        selectedItems.value = collectList.value.map(item => item.productId);
    }
};

// 批量删除
const batchDelete = () => {
    if (selectedItems.value.length === 0) {
        uni.showToast({
            title: t("请选择要取消的商品"),
            icon: "none"
        });
        return;
    }
    
    uni.showModal({
        title: t("提示"),
        content: t(`确定取消收藏${selectedItems.value.length}个商品吗？`),
        success: async (res) => {
            if (res.confirm) {
                try {
                    // 这里应该调用批量删除API，目前逐个删除
                    for (const productId of selectedItems.value) {
                        await delCollectProduct({ id: productId });
                    }
                    
                    uni.showToast({
                        title: t("取消收藏成功"),
                        icon: "none"
                    });
                    
                    selectedItems.value = [];
                    isManageMode.value = false;
                    
                    filterParams.page = 1;
                    collectList.value = [];
                    await __getCollectProductListSilent();
                } catch (error: any) {
                    uni.showToast({
                        title: error.message,
                        icon: "none"
                    });
                }
            }
        }
    });
};

onLoad(() => {
    __getCollectProductList();
});

onReachBottom(() => {
    if (filterParams.page < Math.ceil(total.value / filterParams.size)) {
        filterParams.page++;
        __getCollectProductList();
    }
});

// 页面卸载时确保隐藏loading
onUnmounted(() => {
    try {
        uni.hideLoading();
        console.log('页面卸载，确保隐藏loading');
    } catch (error) {
        console.log('隐藏loading时出错:', error);
    }
});
</script>

<style lang="scss" scoped>
/* 自定义标题栏 */
.custom-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 250rpx;
    background: #fff;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 30rpx 30rpx;
    z-index: 999;
    
    .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .back-icon {
            width: 40rpx;
            height: 40rpx;
            position: relative;
            top: 4px;
        }
    }
    
    .header-title {
        font-size: 35rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
        text-align: center;
        position: relative;
        left: 20px;
    }
    
    .header-right {
        width: 120rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        
        .manage-btn {
            font-size: 28rpx;
            color: #333;
            cursor: pointer;
            position: relative;
            top: 3px;
        }
    }
}

/* 主要内容区域 */
.collect-main {
    min-height: 100vh;
    background: #F6F6F6;
    padding-top: 260rpx;
    padding-bottom: 140rpx; /* 为底部操作栏留出空间 */
}

.collect-product {
    .move-item {
        position: relative;
    }

    .collect-product-item {
        padding: 30rpx;
        background-color: #fff;
        display: flex;
        overflow: hidden;

        .collect-product-item-left {
            .img-box {
                width: 150rpx;
                height: 150rpx;
            }
        }

        .collect-product-item-right {
            width: 100%;
            padding-left: 12rpx;
            .title {
                width: 500rpx;
                font-size: 28rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-bottom: 8rpx;
            }
            
            .shop-name {
                font-size: 24rpx;
                color: #999;
                margin-bottom: 15rpx;
            }

            .item-bottom {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 25rpx;

                .price {
                    font-size: 32rpx;
                    color: #C89B6D;
                    position: relative;
                    top: -5rpx;
                }
            }
        }
    }
    
    .divider {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1rpx;
        background: #E5E5E5;
    }
}

/* 管理模式样式 */
.manage-mode {
    .manage-item {
        position: relative;
        background-color: #fff;
        display: flex;
        align-items: stretch;
        
        .select-checkbox {
            width: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            padding: 30rpx 0;
            
            .checkbox {
                width: 36rpx;
                height: 36rpx;
                border: 2rpx solid #E5E5E5;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                
                &.selected {
                    border-color: #566E9F;
                    background: #566E9F;
                    
                    .checkbox-inner {
                        width: 16rpx;
                        height: 10rpx;
                        border: 2rpx solid #fff;
                        border-top: none;
                        border-right: none;
                        transform: rotate(-45deg) translate(1rpx, -1rpx);
                    }
                }
            }
        }
        
        .collect-product-item {
            flex: 1;
            display: flex;
            padding: 30rpx 30rpx 30rpx 0;
            background-color: #fff;
            overflow: hidden;
        }
        
        .divider {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1rpx;
            background: #E5E5E5;
        }
    }
}

/* 底部操作栏 */
.bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background: #fff;
    border-top: 1rpx solid #E5E5E5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    padding-bottom: calc(0rpx + env(safe-area-inset-bottom));
    z-index: 999;
    
    .select-all-wrapper {
        display: flex;
        align-items: center;
        
        .select-all-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            
            .checkbox {
                width: 36rpx;
                height: 36rpx;
                border: 2rpx solid #E5E5E5;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16rpx;
                transition: all 0.2s ease;
                
                &.selected {
                    border-color: #566E9F;
                    background: #566E9F;
                    
                    .checkbox-inner {
                        width: 16rpx;
                        height: 10rpx;
                        border: 2rpx solid #fff;
                        border-top: none;
                        border-right: none;
                        transform: rotate(-45deg) translate(1rpx, -1rpx);
                    }
                }
            }
            
            .select-all-text {
                font-size: 28rpx;
                color: #333;
            }
        }
    }
    
    .action-btn {
        background:#2F3B50;
        color: #fff;
        padding: 18rpx 130rpx;
        border-radius: 12rpx;
        font-size: 28rpx;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &.disabled {
            background: #E5E5E5;
            color: #999;
            cursor: not-allowed;
        }
        
        &:not(.disabled):active {
            opacity: 0.8;
            transform: scale(0.98);
        }
    }
}

/* 空状态样式 */
.empty-state {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #F6F6F6;
    z-index: 1;
    padding: 0 60rpx;
    box-sizing: border-box;
    
    .empty-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;
        text-align: center;
    }
    
    .empty-subtitle {
        font-size: 25rpx;
        color: #999;
        margin-bottom: 80rpx;
        text-align: center;
        position: relative;
        top: -5px;
    }
    
    .empty-image {
        width: 360rpx;
        height: 280rpx;
    }
}

.collect-move-box {
    width: 150rpx;
    height: 100%;
    color: #fff;
    text-align: center;
    vertical-align: middle;
    display: flex;
    
    text {
        display: block;
        position: absolute;
        top: 50%;
        margin-top: -20rpx;
        font-size: 24rpx;
        text-align: center;
        width: 100%;
        pointer-events: none; /* 防止文字阻止事件 */
    }
    
    .btn-collect {
        width: 150rpx;
        background: var(--vice-bg);
        color: var(--general);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        position: relative;
        cursor: pointer;
        user-select: none;
        -webkit-user-select: none;
    }
    
    .btn-del {
        width: 150rpx;
        background: #2F3B50;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        position: relative;
        cursor: pointer;
        user-select: none;
        -webkit-user-select: none;
        transition: background-color 0.2s ease;
        
        &:active {
            background: #1a2332;
        }
        
        text {
            color: #fff;
            font-size: 24rpx;
            pointer-events: none;
        }
    }
}

/* 优化滑动操作的触摸体验 */
.collect-product {
    overflow: visible;
}

.move-item {
    touch-action: pan-x; /* 只允许水平滑动 */
    -webkit-overflow-scrolling: touch;
}

.uni-swipe-action {
    overflow: visible;
}

.uni-swipe-action-item {
    touch-action: pan-x; /* 只允许水平滑动 */
}

.collect-product-item {
    touch-action: auto; /* 商品内容区域允许正常触摸 */
}

.collect-move-box {
    touch-action: manipulation; /* 优化按钮触摸 */
    user-select: none;
    -webkit-user-select: none;
    position: relative;
    z-index: 10; /* 确保按钮在最上层 */
    
    .btn-del {
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
        position: relative;
        z-index: 11;
        
        /* 增加点击区域 */
        &::before {
            content: '';
            position: absolute;
            top: -10rpx;
            left: -10rpx;
            right: -10rpx;
            bottom: -10rpx;
            z-index: -1;
        }
    }
}
</style>
